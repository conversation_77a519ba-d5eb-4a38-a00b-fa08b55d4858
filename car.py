import torch
import cv2
import os

# 加载YOLOv5模型（选择合适的模型，默认使用 yolov5s）
model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)

# 指定检测目标为'car'类别（可以根据模型支持的类别调整）
model.classes = [2]  # 在COCO数据集中，2对应于'car'

# 加载视频文件
video_path = 'D:\pragram\20240501_20240501125647_20240501140806_125649.mp4'
cap = cv2.VideoCapture(video_path)

# 获取视频的帧率和尺寸
fps = cap.get(cv2.CAP_PROP_FPS)
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

# 定义输出视频文件
output_path = 'output_with_cars.mp4'
fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 保存为MP4格式
out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

# 初始化车辆计数
car_count = 0

# 循环处理每一帧
while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    # 使用YOLOv5检测车辆
    results = model(frame)

    # 提取检测到的车辆
    detections = results.xyxy[0].cpu().numpy()  # 获取检测框（x1, y1, x2, y2, conf, class）

    # 过滤'car'类别的检测结果并统计车辆数量
    cars_in_frame = sum([1 for det in detections if det[5] == 2])  # 2代表'car'
    car_count += cars_in_frame

    # 在帧上绘制检测框
    annotated_frame = results.render()[0]  # 绘制检测框

    # 写入视频
    out.write(annotated_frame)

    # 可以打印当前帧车辆数量，或每隔一定帧数显示信息
    print(f"当前帧检测到 {cars_in_frame} 辆车")

# 释放资源
cap.release()
out.release()
print(f"视频处理完成，输出视频保存为 {output_path}")
print(f"检测到的总车辆数量为: {car_count}")