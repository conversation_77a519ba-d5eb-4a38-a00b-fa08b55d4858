% 直方图均衡化MATLAB代码实现
function histogram_equalization()
    % 读取图像
    img = imread('2.jpg'); % 替换为你的图像路径
    img = rgb2gray(img); % 转换为灰度图像
    
    % 显示原始图像
    figure;
    subplot(1, 2, 1);
    imshow(img);
    title('原始图像');
    
    % 计算原始图像的直方图
    [counts, binLocations] = imhist(img);
    subplot(1, 2, 2);
    bar(binLocations, counts);
    title('原始图像直方图');
    
    % 直方图均衡化
    img_eq = histeq(img);
    
    % 显示均衡化后的图像
    figure;
    subplot(1, 2, 1);
    imshow(img_eq);
    title('均衡化后的图像');
    
    % 计算均衡化后的直方图
    [counts_eq, binLocations_eq] = imhist(img_eq);
    subplot(1, 2, 2);
    bar(binLocations_eq, counts_eq);
    title('均衡化后的直方图');
    
    % 显示原始图像和均衡化后的图像的对比
    figure;
    subplot(1, 2, 1);
    imshow(img);
    title('原始图像');
    
    subplot(1, 2, 2);
    imshow(img_eq);
    title('均衡化后的图像');
end