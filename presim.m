% 1. 读取 Excel 数据，指定 time 作为日期列，number 作为因变量
filename = '4.xlsx';
data = readtable(filename);
    
    % 提取数据
time = data.time; % 假设时间列名为'Time'
    %speed = data.speed; % 车速列名假设为'Speed'
number = data.number; % 流量列名假设为'Flow'
% 2. 绘制原始数据趋势
figure;
plot(time,number);  % number为因变量
title('原始数据趋势');
xlabel('时间');
ylabel('数量');
grid on;

% 3. ADF 检验原始数据平稳性
[~, pValue, ~, stat, ~, cv] = adftest(data.number);  % 使用number列数据进行ADF检验
testResults = struct('tstat', stat, 'pValue', pValue, 'lags', [], 'num_lags', [], 'cValue', cv);
disp('原始序列的ADF检验结果为:');
disp(tagADF(testResults));  % 调用定义在文件结尾的 tagADF 函数

% 4. 绘制自相关图
figure;
autocorr(data.number);  % 自相关图
title('原始数据自相关图');

% 5. 一阶差分
D_data = diff(data.number);  % 对number列数据进行一阶差分
figure;
plot(data.time(2:end), D_data);  % 绘制差分后的数据
title('一阶差分后的数据');
xlabel('时间');
ylabel('差分数量');

% 6. 绘制差分后的自相关和偏自相关图
figure;
autocorr(D_data);  % 差分后的自相关图
title('差分数据自相关图');

figure;
parcorr(D_data);  % 差分后的偏自相关图
title('差分数据偏自相关图');

% 7. ADF 检验差分序列的平稳性
[~, pValue, ~, stat, ~, cv] = adftest(D_data);  % 对差分数据进行ADF检验
testResults = struct('tstat', stat, 'pValue', pValue, 'lags', [], 'num_lags', [], 'cValue', cv);
disp('差分序列的ADF检验结果为:');
disp(tagADF(testResults));

% 8. 白噪声检验（Ljung-Box Test）
[h, pValue] = lbqtest(D_data);  % 白噪声检验
disp('差分序列的白噪声检验结果为:');
disp(['p-value: ', num2str(pValue)]);

% 9. 定阶过程，使用 BIC 选择 ARIMA 模型
pmax = floor(length(D_data) / 10);  % 设置p和q的最大值
qmax = floor(length(D_data) / 10);
bic_matrix = nan(pmax + 1, qmax + 1);

for p = 0:pmax
    for q = 0:qmax
        try
            model = arima(p, 1, q);  % 构建ARIMA模型
            fit = estimate(model, data.number);  % 估计模型参数
            [~,~,logL] = summarize(fit);
            bic_matrix(p + 1, q + 1) = aicbic(logL, numel(fit.ParamNames), numel(data.number), 'BIC');
        catch
            bic_matrix(p + 1, q + 1) = NaN;
        end
    end
end

% 10. 找到 BIC 最小的 p 和 q
[minBIC, idx] = min(bic_matrix(:));
[p, q] = ind2sub(size(bic_matrix), idx);
p = p - 1;
q = q - 1;
disp(['BIC最小的p值和q值为：', num2str(p), ', ', num2str(q)]);

% 11. 建立 ARIMA 模型并进行预测
model = arima(p, 1, q);  % 根据找到的p和q值构建最终模型
fitModel = estimate(model, data.number);  % 拟合模型

% 输出模型报告
summarize(fitModel);

% 12. 预测未来5天的数据
forecastData = forecast(fitModel, 5);  % 预测未来5天的数据
disp('预测未来5天的数据:');
disp(forecastData);

% --------------- 函数定义 ---------------

% ADF 平稳性检验结果格式化函数
function result = tagADF(testResults)
    result = table( ...
        testResults.tstat, ...
        testResults.pValue, ...
        testResults.lags, ...
        testResults.num_lags, ...
        testResults.cValue(1), ...
        testResults.cValue(2), ...
        testResults.cValue(3), ...
        'VariableNames', { ...
            'Test Statistic Value', 'p-value', 'Lags Used', ...
            'Number of Observations Used', 'Critical Value(1%)', ...
            'Critical Value(5%)', 'Critical Value(10%)' ...
        } ...
    );
end
