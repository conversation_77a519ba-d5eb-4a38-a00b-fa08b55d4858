% 1. 读取 Excel 文件
filename = 'result.xlsx'; % 替换为您的文件名
sheetNames = sheetnames(filename); % 获取所有工作表的名称

% 2. 存储每个工作表的数据
data = cell(length(sheetNames), 1); % 创建一个元胞数组来存储每个工作表的数据

for i = 1:length(sheetNames)
    data{i} = readtable(filename, 'Sheet', sheetNames{i}, 'ReadVariableNames', false, 'ReadRowNames', false);
    data{i}.Properties.VariableNames = {'Index', 'Value1', 'Value2'}; % 修改列名
end

% 3. 显示每个工作表的数据
for i = 1:length(sheetNames)
    fprintf('工作表 %s 的数据:\n', sheetNames{i});
    disp(data{i});
end

% 4. 进行 ADF 检验
for i = 1:length(sheetNames)
    y1 = data{i}.Value1;
    y2 = data{i}.Value2;
    
    % 进行 ADF 检验
    [h1, pValue1, stat1, cValue1] = adftest(y1, 'Model', 'ARD', 'Lags', 1);
    [h2, pValue2, stat2, cValue2] = adftest(y2, 'Model', 'ARD', 'Lags', 1);
    
    % 输出结果
    fprintf('ADF 检验结果 - 工作表 %s - Value1\n', sheetNames{i});
    fprintf(['H0 是否被拒绝: ', num2str(h1), '\n']);
    fprintf(['p 值: ', num2str(pValue1), '\n']);
    fprintf(['检验统计量: ', num2str(stat1), '\n']);
    fprintf(['临界值: ', num2str(cValue1), '\n']);
    
    fprintf('ADF 检验结果 - 工作表 %s - Value2\n', sheetNames{i});
    fprintf(['H0 是否被拒绝: ', num2str(h2), '\n']);
    fprintf(['p 值: ', num2str(pValue2), '\n']);
    fprintf(['检验统计量: ', num2str(stat2), '\n']);
    fprintf(['临界值: ', num2str(cValue2), '\n']);
end

% 5. 绘制每个工作表的数据
figure;
for i = 1:length(sheetNames)
    subplot(length(sheetNames), 1, i);
    plot(data{i}.Index, data{i}.Value1, 'b-', 'LineWidth', 1.5);
    hold on;
    plot(data{i}.Index, data{i}.Value2, 'r--', 'LineWidth', 1.5);
    title(['工作表 ', sheetNames{i}, ' 的数据']);
    xlabel('时间点');
    ylabel('值');
    legend('Value1', 'Value2');
    grid on;
end