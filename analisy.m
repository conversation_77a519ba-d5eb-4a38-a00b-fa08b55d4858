clear all
% 指定包含Excel文件的文件夹路径
folderPath = 'D:\pragram\graduate s\pythonProject1\data'; % 替换为你的文件夹路径

% 获取文件夹内所有Excel文件的列表
filePattern = fullfile(folderPath, '*.xlsx'); % 或者使用 '*.xls' 如果是旧版本Excel
excelFiles = dir(filePattern);

% 循环遍历每个Excel文件
for k = 1:length(excelFiles)
    % 构建完整的文件路径
    filePath = fullfile(excelFiles(k).folder, excelFiles(k).name);
    
    % 读取Excel文件
    data = readtable(filePath);
    
    % 提取数据
    time = data.time; % 假设时间列名为'Time'
    %speed = data.speed; % 车速列名假设为'Speed'
    flow = data.number; % 流量列名假设为'Flow'
    %density = data.Density; % 密度列名假设为'Density'
    
    % 创建一个新的图形窗口
    figure;
    
    % 绘制时间与车速的关系
    %plot(time, speed, '-o', 'DisplayName', 'Speed');
    %hold on; % 保持当前图形，以便在同一图上绘制其他线条
    
    % 绘制时间与流量的关系
    plot(time, flow, '-.', 'DisplayName', 'Flow');
    
    % 绘制时间与密度的关系
    %plot(time, density, '-s', 'DisplayName', 'Density');
    
    % 添加图例
    legend show;
    
    % 设置图形标题和坐标轴标签
    title(['Data', excelFiles(k).name]);
    xlabel('Time');
    ylabel('Values');
    
    ylim([50 230]);
    % 自动调整坐标轴范围
    axis tight;
    
    % 释放hold状态
    hold off;
end