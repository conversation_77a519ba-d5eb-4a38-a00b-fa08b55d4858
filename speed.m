clear all;

% 定义输入和输出文件夹路径
inputFolder = 'D:\pragram\graduate s\pythonProject1\speed'; % 替换为你的输入文件夹路径
outputFolder = 'D:\pragram\graduate s\pythonProject1\speed-output'; % 替换为你的输出文件夹路径

% 创建输出文件夹（如果不存在）
if ~exist(outputFolder, 'dir')
    mkdir(outputFolder);
end

% 获取文件夹中所有的 .csv 文件
files = dir(fullfile(inputFolder, '*.csv'));

% 遍历每个 .csv 文件
for k = 1:length(files)
    % 构建完整的文件路径
    filePath = fullfile(inputFolder, files(k).name);
    
    % 读取 .csv 文件
    try
        data = readtable(filePath);
    catch ME
        warning('无法读取文件 %s: %s', files(k).name, ME.message);
        continue;
    end
    
    % 检查数据表是否有至少两列
    if height(data) < 2 || width(data) < 2
        warning('文件 %s 包含的数据不足以绘制散点图。', files(k).name);
        continue;
    end
    
    % 提取前两列作为 x 和 y 数据
    x = data{:, 1};
    y = data{:, 2};
    
    % 剔除大于180的数据
    valid_indices = y <= 180; % 只考虑速度不超过180的情况
    x = x(valid_indices);
    y = y(valid_indices);
    
    % 创建新的图形窗口
    figure;
    hold on
    % 绘制散点图，点的颜色设置为灰色
    scatter(x, y, 50, 'r', '.');
    
    % 添加y轴60-80范围的半透明阴影
    xlim_values = xlim;
    fill([xlim_values(1) xlim_values(2) xlim_values(2) xlim_values(1)], [60 60 80 80], 'g', 'FaceAlpha', 0.2, 'EdgeColor', 'none');
    
    % 设置图表属性
    title(['散点图 - ', files(k).name]);
    xlabel('X轴');
    ylabel('Y轴');
    hold on;
    
    % 添加图例
    legend('数据点', 'Location', 'best');
    
    % 保存图表为图像文件
    outputFilePath = fullfile(outputFolder, [files(k).name '.png']);
    saveas(gcf, outputFilePath);
    
    % 关闭当前图形窗口
    close(gcf);
end

disp('所有文件已处理完毕！');