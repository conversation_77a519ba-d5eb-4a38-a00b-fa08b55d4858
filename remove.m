%% 批量处理图片 - 去雾、去噪和超分辨率
% 设置输入和输出文件夹
inputFolder = 'input_images'; % 原始图片文件夹
outputFolder = 'output_images'; % 处理后图片保存文件夹
if ~exist(outputFolder, 'dir')
    mkdir(outputFolder);
end

% 获取所有图片文件
imageFiles = dir(fullfile(inputFolder, '*.*'));
imageFiles = imageFiles(~[imageFiles.isdir]);

% 遍历每张图片进行处理
for i = 1:length(imageFiles)
    % 读取图片
    imagePath = fullfile(inputFolder, imageFiles(i).name);
    image = imread(imagePath);
    
    fprintf('Processing image: %s\n', imageFiles(i).name);

    %% 去雾（示例：使用Retinex算法）
    dehazedImage = retinexDehaze(image);

    %% 去噪（示例：使用彩色中值滤波）
    denoisedImage = zeros(size(dehazedImage), 'like', dehazedImage);
    for channel = 1:size(dehazedImage, 3)
        denoisedImage(:, :, channel) = medfilt2(dehazedImage(:, :, channel), [3, 3]);
    end

    %% 超分辨率（示例：使用双三次插值）
    scaleFactor = 4; % 放大倍数
    superResolvedImage = imresize(denoisedImage, scaleFactor, 'bicubic');

    %% 保存处理后图片
    [~, name, ext] = fileparts(imageFiles(i).name);
    outputFilePath = fullfile(outputFolder, [name, '_processed', ext]);
    imwrite(superResolvedImage, outputFilePath);

    fprintf('Saved processed image to: %s\n', outputFilePath);
end

%% 自定义去雾函数示例（Retinex）
function output = retinexDehaze(inputImage)
    % 这里用一个简单的对数变换作为Retinex示例
    inputImage = im2double(inputImage);
    output = log(1 + inputImage) ./ log(1 + max(inputImage(:)));
    output = im2uint8(output); % 转换回uint8格式
end
