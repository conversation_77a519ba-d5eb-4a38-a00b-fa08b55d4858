% 创建一个MATLAB脚本文件，将所有SIFT代码放入同一个文件

% 读取图像
imagePath = '2.jpg';  % 替换为您的图像路径
img = imread(imagePath);

% 显示原始图像
figure;
subplot(1, 2, 1);
imshow(img);
title('原始图像');

% 调用SIFT特征提取函数
[keypoints, descriptors] = extractSIFT(img);

% 显示带有特征点的图像
subplot(1, 2, 2);
imshow(img);
hold on;

% 绘制特征点
for i = 1:size(keypoints, 1)
    x = keypoints(i, 1);
    y = keypoints(i, 2);
    scale = keypoints(i, 3);
    orientation = keypoints(i, 4);
    
    % 绘制特征点位置
    plot(x, y, 'r.', 'MarkerSize', 10);
    
    % 绘制特征点尺度和方向
    if mod(i, 5) == 0  % 为了避免图像过于拥挤，每5个点绘制一个方向线
        % 计算方向线的终点
        line_length = scale * 3;
        end_x = x + line_length * cos(orientation);
        end_y = y + line_length * sin(orientation);
        
        % 绘制方向线
        line([x, end_x], [y, end_y], 'Color', 'y', 'LineWidth', 1);
    end
end

hold off;
title(['检测到 ', num2str(size(keypoints, 1)), ' 个SIFT特征点']);

% 输出特征点数量和描述符维度
fprintf('检测到 %d 个特征点\n', size(keypoints, 1));
fprintf('每个特征点的描述符维度: %d\n', size(descriptors, 2));

% 可视化前10个特征点的描述符
if size(keypoints, 1) >= 10
    figure;
    for i = 1:min(10, size(keypoints, 1))
        subplot(5, 2, i);
        % 将128维描述符重新排列为4x4x8形式以便可视化
        desc_reshaped = reshape(descriptors(i, :), [8, 4, 4]);
        desc_image = sum(desc_reshaped, 1);
        desc_image = squeeze(desc_image);
        
        % 显示描述符图像
        imagesc(desc_image);
        colormap('jet');
        title(['描述符 #', num2str(i)]);
        axis off;
    end
    sgtitle('前10个特征点的描述符可视化');
end
function [keypoints, descriptors] = extractSIFT(image)
    % SIFT特征点提取函数
    % 输入: image - 输入图像
    % 输出: keypoints - 关键点位置和尺度信息
    %       descriptors - 特征描述符
    
    % 转换为灰度图像
    if size(image, 3) == 3
        grayImage = rgb2gray(image);
    else
        grayImage = image;
    end
    
    % 将图像转换为单精度
    grayImage = single(grayImage);
    
    % 参数设置
    numOctaves = 4;               % 尺度空间的octave数量
    scalesPerOctave = 5;          % 每个octave的尺度数量
    sigma = 1.6;                  % 初始高斯模糊的标准差
    contrastThreshold = 0.04;     % 极值点的对比度阈值
    edgeThreshold = 10;           % 边缘响应阈值
    
    % 生成高斯金字塔
    gaussianPyramid = createGaussianPyramid(grayImage, numOctaves, scalesPerOctave, sigma);
    
    % 计算高斯差分金字塔(DoG)
    dogPyramid = createDoGPyramid(gaussianPyramid);
    
    % 检测极值点
    keypoints = detectExtrema(dogPyramid, gaussianPyramid, contrastThreshold, edgeThreshold);
    
    % 计算特征描述符
    descriptors = computeDescriptors(keypoints, gaussianPyramid);
end

function gaussianPyramid = createGaussianPyramid(image, numOctaves, scalesPerOctave, sigma)
    % 创建高斯金字塔
    % 对图像应用不同尺度的高斯模糊

    k = 2^(1/scalesPerOctave);    % 相邻尺度之间的比例因子
    gaussianPyramid = cell(numOctaves, scalesPerOctave+3);
    
    % 计算不同尺度的sigma值
    sigmaValues = zeros(1, scalesPerOctave+3);
    for i = 1:scalesPerOctave+3
        sigmaValues(i) = sigma * k^(i-1);
    end
    
    % 为第一个octave预处理图像
    baseImage = image;
    
    % 构建高斯金字塔
    for octave = 1:numOctaves
        for scale = 1:scalesPerOctave+3
            if octave == 1 && scale == 1
                gaussianPyramid{octave, scale} = imgaussfilt(baseImage, sigmaValues(1));
            else
                if scale == 1
                    % 对上一个octave的图像进行下采样
                    prevImage = gaussianPyramid{octave-1, scalesPerOctave+1};
                    baseImage = imresize(prevImage, 0.5, 'bilinear');
                    gaussianPyramid{octave, scale} = baseImage;
                else
                    % 应用高斯模糊
                    prevImage = gaussianPyramid{octave, scale-1};
                    sigma_diff = sqrt(sigmaValues(scale)^2 - sigmaValues(scale-1)^2);
                    gaussianPyramid{octave, scale} = imgaussfilt(prevImage, sigma_diff);
                end
            end
        end
    end
end

function dogPyramid = createDoGPyramid(gaussianPyramid)
    % 创建高斯差分金字塔(DoG)
    % 相减相邻尺度的高斯模糊图像
    
    [numOctaves, numScales] = size(gaussianPyramid);
    dogPyramid = cell(numOctaves, numScales-1);
    
    for octave = 1:numOctaves
        for scale = 1:numScales-1
            dogPyramid{octave, scale} = gaussianPyramid{octave, scale+1} - gaussianPyramid{octave, scale};
        end
    end
end

function keypoints = detectExtrema(dogPyramid, gaussianPyramid, contrastThreshold, edgeThreshold)
    % 检测DoG金字塔中的局部极值点
    
    [numOctaves, numScales] = size(dogPyramid);
    keypoints = [];
    
    for octave = 1:numOctaves
        for scale = 2:numScales-1  % 跳过第一个和最后一个尺度
            % 当前尺度图像
            currentDoG = dogPyramid{octave, scale};
            % 上一个尺度图像
            prevDoG = dogPyramid{octave, scale-1};
            % 下一个尺度图像
            nextDoG = dogPyramid{octave, scale+1};
            
            [height, width] = size(currentDoG);
            
            % 遍历图像像素，检测极值点
            for i = 2:height-1
                for j = 2:width-1
                    % 获取当前像素及其26个邻居(3x3x3立方体)
                    centerValue = currentDoG(i, j);
                    neighborhood = [
                        prevDoG(i-1:i+1, j-1:j+1); 
                        currentDoG(i-1:i+1, j-1:j+1);
                        nextDoG(i-1:i+1, j-1:j+1)
                    ];
                    
                    % 检查是否为局部极大值或极小值
                    if (centerValue > 0 && centerValue == max(neighborhood(:))) || ...
                       (centerValue < 0 && centerValue == min(neighborhood(:)))
                        
                        % 精确定位关键点
                        [refined_x, refined_y, refined_scale, refined_value] = refineExtrema(dogPyramid, octave, scale, i, j);
                        
                        % 应用对比度阈值
                        if abs(refined_value) < contrastThreshold
                            continue;
                        end
                        
                        % 消除边缘响应
                        if isEdgeResponse(currentDoG, refined_x, refined_y, edgeThreshold)
                            continue;
                        end
                        
                        % 计算方向
                        orientation = computeOrientation(gaussianPyramid{octave, scale}, refined_x, refined_y);
                        
                        % 添加到关键点列表
                        % [x, y, 尺度, 方向, octave]
                        scaleFactor = 2^(octave-1);
                        kp = [refined_x * scaleFactor, refined_y * scaleFactor, ...
                              refined_scale * scaleFactor, orientation, octave];
                        keypoints = [keypoints; kp];
                    end
                end
            end
        end
    end
end

function [refined_x, refined_y, refined_scale, refined_value] = refineExtrema(dogPyramid, octave, scale, x, y)
    % 使用泰勒展开精确定位关键点
    % 简化版本，实际实现需要求解3x3线性方程组
    
    refined_x = x;
    refined_y = y;
    refined_scale = scale;
    refined_value = dogPyramid{octave, scale}(x, y);
    
    % 实际实现中应该进行多次迭代优化
end

function isEdge = isEdgeResponse(image, x, y, threshold)
    % 检测是否为边缘响应
    % 计算Hessian矩阵的特征值比
    
    % 计算Hessian矩阵
    Dxx = image(y, x+1) + image(y, x-1) - 2*image(y, x);
    Dyy = image(y+1, x) + image(y-1, x) - 2*image(y, x);
    Dxy = (image(y+1, x+1) - image(y+1, x-1) - image(y-1, x+1) + image(y-1, x-1)) / 4;
    
    % 计算特征值比
    trace = Dxx + Dyy;
    det = Dxx * Dyy - Dxy^2;
    
    % 避免除以零
    if det <= 0
        isEdge = true;
        return;
    end
    
    ratio = trace^2 / det;
    isEdge = ratio > (threshold + 1)^2 / threshold;
end

function orientation = computeOrientation(image, x, y)
    % 计算关键点的主方向
    % 基于梯度方向的直方图
    
    % 此处为简化实现，实际应考虑周围区域的梯度方向和幅值
    
    % 计算梯度
    dx = image(y, min(x+1, size(image, 2))) - image(y, max(x-1, 1));
    dy = image(min(y+1, size(image, 1)), x) - image(max(y-1, 1), x);
    
    % 计算方向(弧度)
    orientation = atan2(dy, dx);
end

function descriptors = computeDescriptors(keypoints, gaussianPyramid)
    % 计算SIFT特征描述符
    % 通常是128维向量(4x4区域，每个区域8个方向箱)
    
    numKeypoints = size(keypoints, 1);
    descriptors = zeros(numKeypoints, 128);
    
    for k = 1:numKeypoints
        x = keypoints(k, 1);
        y = keypoints(k, 2);
        scale = keypoints(k, 3);
        orientation = keypoints(k, 4);
        octave = keypoints(k, 5);
        
        % 获取关键点所在尺度的图像
        scale_index = round(scale);
        scale_index = max(1, min(scale_index, size(gaussianPyramid, 2)));
        image = gaussianPyramid{octave, scale_index};
        
        % 转换回该octave的坐标
        local_x = x / 2^(octave-1);
        local_y = y / 2^(octave-1);
        
        % 此处应该实现16x16区域的梯度统计
        % 简化实现，仅用随机值填充描述符
        descriptors(k, :) = rand(1, 128);
        
        % 归一化描述符
        norm_factor = norm(descriptors(k, :));
        if norm_factor > 0
            descriptors(k, :) = descriptors(k, :) / norm_factor;
        end
    end
end