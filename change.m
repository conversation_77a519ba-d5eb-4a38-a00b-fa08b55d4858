% 脚本文件批量处理图片

% 设置输入和输出文件夹
input_folder = 'input_images'; % 修改为你的输入文件夹路径
output_folder = 'output_images'; % 修改为你的输出文件夹路径

% 检查输入文件夹是否存在
if ~isfolder(input_folder)
    error('输入文件夹不存在。');
end

% 创建输出文件夹（如果不存在）
if ~isfolder(output_folder)
    mkdir(output_folder);
end

% 获取输入文件夹中的所有图片文件
image_files = dir(fullfile(input_folder, '*.*'));
valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'};

for i = 1:length(image_files)
    [~, ~, ext] = fileparts(image_files(i).name);
    if ~ismember(lower(ext), valid_extensions)
        continue; % 跳过不支持的文件
    end

    input_file = fullfile(input_folder, image_files(i).name);
    output_file = fullfile(output_folder, [image_files(i).name(1:end-length(ext)), '.jpg']);

    try
        resize_image_to_jpg(input_file, output_file);
        fprintf('成功处理文件：%s\n', input_file);
    catch ME
        fprintf('处理文件失败：%s，错误信息：%s\n', input_file, ME.message);
    end
end

function resize_image_to_jpg(input_file, output_file)
    % 检查输入文件是否存在
    if ~isfile(input_file)
        error('输入文件不存在。');
    end

    % 读取图像
    img = imread(input_file);

    % 获取图像大小
    [height, width, ~] = size(img);

    % 计算16:9的目标大小
    target_aspect_ratio = 16 / 9;

    if width / height > target_aspect_ratio
        % 宽度过大，裁剪宽度
        new_width = floor(height * target_aspect_ratio);
        crop_x = floor((width - new_width) / 2);
        img_cropped = img(:, crop_x+1:crop_x+new_width, :);
    else
        % 高度过大，裁剪高度
        new_height = floor(width / target_aspect_ratio);
        crop_y = floor((height - new_height) / 2);
        img_cropped = img(crop_y+1:crop_y+new_height, :, :);
    end

    % 调整图像大小
    resized_img = imresize(img_cropped, [720, 1280]); % 调整为720p分辨率

    % 初始化JPEG压缩质量
    quality = 90;

    % 保存图像并调整大小至100-200KB
    while true
        % 使用指定质量保存图像
        imwrite(resized_img, output_file, 'jpg', 'Quality', quality);

        % 获取文件大小
        file_info = dir(output_file);
        file_size_kb = file_info.bytes / 1024;

        % 检查文件大小是否在目标范围内
        if file_size_kb >= 100 && file_size_kb <= 200
            break;
        elseif file_size_kb > 200
            % 如果文件太大，降低质量
            quality = quality - 5;
        elseif file_size_kb < 100
            % 如果文件太小，增加质量
            quality = quality + 5;
        end

        % 防止质量设置超出范围
        if quality <= 0 || quality > 100
            error('无法将文件大小调整到100-200KB范围内。');
        end
    end

    fprintf('图像已成功保存为 %s，文件大小：%.2f KB\n', output_file, file_size_kb);
end
