import matplotlib.pyplot as plt
import numpy as np

# 设置字体支持
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_comparison_chart():
    # 数据准备 - 三组指标
    metrics = ['Parameters', 'FLOPs', 'ModelNet Accuracy']

    # Before模型数据
    before_values = [12.55, 4.8, 91.8]
    # New模型数据
    new_values = [12.65, 4.8, 92.0]

    # 为了在同一图表中显示不同量级的数据，需要标准化处理
    # 将所有数据标准化到0-25范围，准确率使用右侧纵轴

    # 左侧纵轴数据 (标准化到0-25)
    before_normalized = [
        12.55,        # 参数量 (M) - 直接显示
        4.8 * 2,      # FLOP (G) - 乘以2以便显示
    ]

    new_normalized = [
        12.65,        # 参数量 (M) - 直接显示
        4.8 * 2,      # FLOP (G) - 乘以2以便显示
    ]
    
    # 创建图形和子图
    fig, ax1 = plt.subplots(figsize=(16, 8))
    
    # 设置柱子的位置
    x = np.arange(len(metrics))
    width = 0.35  # 柱子宽度
    
    # 左侧纵轴的柱状图 (前2个指标)
    bars_before = ax1.bar(x[:2] - width/2, before_normalized, width,
                         label='Before', color='#FF6B6B', alpha=0.8,
                         edgecolor='black', linewidth=0.5)
    bars_new = ax1.bar(x[:2] + width/2, new_normalized, width,
                      label='New', color='#4ECDC4', alpha=0.8,
                      edgecolor='black', linewidth=0.5)

    # 设置左侧纵轴
    ax1.set_xlabel('Performance Indicators', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Metrics', fontsize=14, fontweight='bold')
    ax1.set_ylim(0, 15)
    ax1.tick_params(axis='y', labelcolor='black', labelsize=12)
    ax1.tick_params(axis='x', labelsize=12)

    # 创建右侧纵轴显示准确率
    ax2 = ax1.twinx()
    bars_acc_before = ax2.bar(x[2] - width/2, before_values[2], width,
                             color='#FF6B6B', alpha=0.8,
                             edgecolor='black', linewidth=0.5)
    bars_acc_new = ax2.bar(x[2] + width/2, new_values[2], width,
                          color='#4ECDC4', alpha=0.8,
                          edgecolor='black', linewidth=0.5)
    
    # 设置右侧纵轴
    ax2.set_ylabel('ModelNet40 Accuracy (%)', fontsize=14, fontweight='bold', color='#D35400')
    ax2.set_ylim(91.5, 92.2)  # 聚焦在91.8-91.9范围
    ax2.tick_params(axis='y', labelcolor='#D35400', labelsize=12)
    
    # 设置x轴标签
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics, fontsize=12, fontweight='bold', rotation=0)
    
    # 添加数值标签
    def add_value_labels_left(bars, values, units):
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax1.annotate(f'{values[i]}{units[i]}',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 3),  # 3 points vertical offset
                        textcoords="offset points",
                        ha='center', va='bottom',
                        fontsize=10, fontweight='bold')
    
    def add_value_labels_right(bar, value, unit):
        height = bar.get_height()
        ax2.annotate(f'{value}{unit}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),  # 3 points vertical offset
                    textcoords="offset points",
                    ha='center', va='bottom',
                    fontsize=10, fontweight='bold')
    
    # 为柱子添加数值标签
    before_units = ['M', 'G']
    new_units = ['M', 'G']

    add_value_labels_left(bars_before, before_values[:2], before_units)
    add_value_labels_left(bars_new, new_values[:2], new_units)
    add_value_labels_right(bars_acc_before[0], before_values[2], '%')
    add_value_labels_right(bars_acc_new[0], new_values[2], '%')
    
    # 添加图例
    ax1.legend(['Before', 'New'], loc='upper left', bbox_to_anchor=(0, 0.95), 
               fontsize=12, framealpha=0.9)
    
    # 添加网格
    ax1.grid(True, alpha=0.3, linestyle='--')
    ax1.set_axisbelow(True)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('model_comparison_chart.png', dpi=300, bbox_inches='tight')
    plt.savefig('model_comparison_chart.pdf', bbox_inches='tight')
    plt.savefig('model_comparison_chart.svg', bbox_inches='tight')  # 添加SVG矢量格式
    
    print("✅ Chart generated successfully:")
    print("   - model_comparison_chart.png (High-resolution PNG)")
    print("   - model_comparison_chart.pdf (Vector PDF)")
    print("   - model_comparison_chart.svg (Vector SVG)")
    
    # 显示图表
    plt.show()

if __name__ == "__main__":
    create_comparison_chart()
