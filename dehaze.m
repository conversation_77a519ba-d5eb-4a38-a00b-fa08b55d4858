% Load your hazy/foggy image
hazy_image = imread('2.jpg');

% Apply the dehazing algorithm
dehazed_image = darkChannelPriorDehazing(hazy_image);

% Display results
figure;
subplot(1,2,1); imshow(hazy_image); title('原始图片');
subplot(1,2,2); imshow(dehazed_image); title('去雾后图片');

function [dehazedImage] = darkChannelPriorDehazing(hazy_image, omega, t0)
% DARKCHANNELPRIORDEHAZING Dehaze an image using the Dark Channel Prior 
% algorithm
%   This function implements the dark channel prior algorithm for dehazing
%   and defogging images as proposed by <PERSON> et al.
%
% Inputs:
%   hazy_image: RGB image with fog/haze (double in range [0,1])
%   omega: parameter controlling the strength of dehazing (default: 0.95)
%   t0: minimum transmission value to prevent over-amplification (default: 0.1)
%
% Output:
%   dehazedImage: Dehazed RGB image

% Set default parameters if not provided
if nargin < 2
    omega = 0.95;
end
if nargin < 3
    t0 = 0.1;
end

% Read the image if a filename is provided
if ischar(hazy_image)
    hazy_image = im2double(imread(hazy_image));
end

% Convert to double if not already
if ~isa(hazy_image, 'double')
    hazy_image = im2double(hazy_image);
end

% Get image size
[h, w, ~] = size(hazy_image);

% Estimate atmospheric light (A)
dark_channel = getDarkChannel(hazy_image);
[~, indices] = sort(dark_channel(:), 'descend');
brightest_pixels = indices(1:round(h*w*0.001));
[y_indices, x_indices] = ind2sub([h, w], brightest_pixels);

A = zeros(1, 1, 3);
for i = 1:3
    channel = hazy_image(:,:,i);
    A(1,1,i) = mean(channel(sub2ind([h,w], y_indices, x_indices)));
end

% Estimate transmission map
transmission = estimateTransmission(hazy_image, A, omega);

% Refine transmission using guided filter
gray_image = rgb2gray(hazy_image);
transmission = guidedFilter(gray_image, transmission, 60, 0.0001);

% Ensure minimum transmission
transmission = max(transmission, t0);

% Recover the scene radiance
dehazedImage = zeros(size(hazy_image));
for i = 1:3
    dehazedImage(:,:,i) = (hazy_image(:,:,i) - A(1,1,i)) ./ transmission + A(1,1,i);
end

% Clip values to [0, 1] range
dehazedImage = min(max(dehazedImage, 0), 1);
end

function dark_channel = getDarkChannel(image, patch_size)
% Get the dark channel of an image
% patch_size: the size of local patch (default: 15)

if nargin < 2
    patch_size = 15;
end

padded = padarray(image, [floor(patch_size/2), floor(patch_size/2)], 'symmetric');
[h, w, ~] = size(image);
dark_channel = zeros(h, w);

for i = 1:h
    for j = 1:w
        patch = padded(i:i+patch_size-1, j:j+patch_size-1, :);
        dark_channel(i,j) = min(min(patch(:)));
    end
end
end

function transmission = estimateTransmission(image, A, omega, patch_size)
% Estimate the transmission map
% omega: parameter controlling the amount of haze preserved (default: 0.95)
% patch_size: the size of local patch (default: 15)

if nargin < 3
    omega = 0.95;
end
if nargin < 4
    patch_size = 15;
end

normalized_image = zeros(size(image));
for i = 1:3
    normalized_image(:,:,i) = image(:,:,i) ./ A(1,1,i);
end

dark_channel = getDarkChannel(normalized_image, patch_size);
transmission = 1 - omega * dark_channel;
end

function q = guidedFilter(I, p, r, eps)
% GUIDEDFILTER Implementation of guided filter
%   I: guidance image (grayscale or color)
%   p: filtering input (should be a single channel/grayscale image)
%   r: local window radius
%   eps: regularization parameter
%   q: filtered output

[h, w, c] = size(I);
N = boxfilter(ones(h, w), r);

mean_I = zeros(h, w, c);
for i = 1:c
    mean_I(:,:,i) = boxfilter(I(:,:,i), r) ./ N;
end

mean_p = boxfilter(p, r) ./ N;

mean_Ip = zeros(h, w, c);
for i = 1:c
    mean_Ip(:,:,i) = boxfilter(I(:,:,i) .* p, r) ./ N;
end

cov_Ip = mean_Ip - mean_I .* mean_p;

mean_II = zeros(h, w, c, c);
for i = 1:c
    for j = 1:c
        mean_II(:,:,i,j) = boxfilter(I(:,:,i) .* I(:,:,j), r) ./ N;
    end
end

var_I = zeros(h, w, c, c);
for i = 1:c
    for j = 1:c
        var_I(:,:,i,j) = mean_II(:,:,i,j) - mean_I(:,:,i) .* mean_I(:,:,j);
    end
end

a = zeros(h, w, c);
for i = 1:h
    for j = 1:w
        Sigma = reshape(var_I(i,j,:,:), [c, c]);
        cov = reshape(cov_Ip(i,j,:), [c, 1]);
        a(i,j,:) = (Sigma + eps * eye(c)) \ cov;
    end
end

b = mean_p;
for i = 1:c
    b = b - a(:,:,i) .* mean_I(:,:,i);
end

q = zeros(h, w);
for i = 1:c
    q = q + a(:,:,i) .* I(:,:,i);
end
q = q + b;
end

function imDst = boxfilter(imSrc, r)
% BOXFILTER Fast box filtering implementation
%   imSrc: input image
%   r: radius of box filter
%   imDst: output image

[h, w] = size(imSrc);
imDst = zeros(size(imSrc));

% Cumulative sum over Y axis
imCum = cumsum(imSrc, 1);
% Difference over Y axis
imDst(1:r+1, :) = imCum(r+1:2*r+1, :);
imDst(r+2:h-r, :) = imCum(2*r+2:h, :) - imCum(1:h-2*r-1, :);
imDst(h-r+1:h, :) = repmat(imCum(h, :), [r, 1]) - imCum(h-2*r:h-r-1, :);

% Cumulative sum over X axis
imCum = cumsum(imDst, 2);
% Difference over X axis
imDst(:, 1:r+1) = imCum(:, r+1:2*r+1);
imDst(:, r+2:w-r) = imCum(:, 2*r+2:w) - imCum(:, 1:w-2*r-1);
imDst(:, w-r+1:w) = repmat(imCum(:, w), [1, r]) - imCum(:, w-2*r:w-r-1);
end