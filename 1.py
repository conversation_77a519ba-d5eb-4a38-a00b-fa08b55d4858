import matplotlib.pyplot as plt
import matplotlib.patches as patches

# 设置全局字体大小
plt.rcParams.update({'font.size': 16})

# 输入点数
points = [256, 512, 1024, 2048, 4096, 8192, 16384, 32768]

# 数据
data = {
    'PointMamba': {
        'FLOPs(G)': [3.845024768] * 8,
        '显存(MB)': [0.29,0.31,0.35,0.5,0.6,1.1,2.3,3.2]
    },
    'PointDefMamba': {
        'FLOPs(G)': [4.782281216] * 8,
        '显存(MB)': [0.5,0.7,1.1,1.5,1.7,2.5,3.1,4.5]
    },
    'Point-MAE': {
        'FLOPs(G)': [4.8, 5, 7, 10, 50, 150, 400, 696],
        '显存(MB)': [4.8, 5, 5.2, 6, 6.5, 8, 15, 50.52]
    }
}



# 画 FLOPs 图
plt.figure(figsize=(12, 6))

# 创建均匀分布的x轴位置
x_positions = list(range(len(points)))  # [0, 1, 2, 3, 4, 5, 6, 7]

for model, values in data.items():
    if model == 'PointDefMamba':
        plt.plot(x_positions, values['FLOPs(G)'], marker='*', color='red', markersize=12, linewidth=2, label=model)
    else:
        plt.plot(x_positions, values['FLOPs(G)'], marker='o', markersize=8, linewidth=2, label=model)

# 在32768位置（索引7）添加红色箭头，从Point-MAE连接到PointDefMamba
arrow_x = x_positions[-1]  # 最后一个位置，对应32768
point_mae_y = data['Point-MAE']['FLOPs(G)'][-1]  # Point-MAE在32768处的值
pointdefmamba_y = data['PointDefMamba']['FLOPs(G)'][-1]  # PointDefMamba在32768处的值

plt.annotate('down', xy=(arrow_x, pointdefmamba_y), xytext=(arrow_x, point_mae_y),
            arrowprops=dict(arrowstyle='->', color='red', lw=3),
            fontsize=18, ha='center', va='bottom', weight='bold', color='red')

plt.title('FLOPs', fontsize=20)
plt.xlabel('Input Points', fontsize=18)
plt.ylabel('FLOPs (G)', fontsize=18)
plt.xticks(x_positions, [str(p) for p in points], fontsize=14)  # 使用均匀分布的位置，显示实际数值
plt.yticks(fontsize=14)
plt.grid(False)
plt.legend(fontsize=14)
plt.tight_layout()
plt.savefig('flops_chart.svg', format='svg', dpi=300, bbox_inches='tight')  # 保存为矢量图
plt.show()

# 画 显存 图
plt.figure(figsize=(12, 6))
for model, values in data.items():
    if model == 'PointDefMamba':
        plt.plot(x_positions, values['显存(MB)'], marker='*', color='red', markersize=12, linewidth=2, label=model)
    else:
        plt.plot(x_positions, values['显存(MB)'], marker='o', markersize=8, linewidth=2, label=model)

# 在32768位置（索引7）添加红色箭头，从Point-MAE连接到PointDefMamba
arrow_x = x_positions[-1]  # 最后一个位置，对应32768
point_mae_y = data['Point-MAE']['显存(MB)'][-1]  # Point-MAE在32768处的值
pointdefmamba_y = data['PointDefMamba']['显存(MB)'][-1]  # PointDefMamba在32768处的值

plt.annotate('down', xy=(arrow_x, pointdefmamba_y), xytext=(arrow_x, point_mae_y),
            arrowprops=dict(arrowstyle='->', color='red', lw=3),
            fontsize=18, ha='center', va='bottom', weight='bold', color='red')

plt.title('GPU Memory', fontsize=20, weight='bold')
plt.xlabel('Input Points', fontsize=18, weight='bold')
plt.ylabel('Memory Usage (MB)', fontsize=18, weight='bold')
plt.xticks(x_positions, [str(p) for p in points], fontsize=14)  # 使用均匀分布的位置，显示实际数值
plt.yticks(fontsize=14)
plt.grid(False)
plt.legend(fontsize=14)
plt.tight_layout()
plt.savefig('memory_chart.svg', format='svg', dpi=300, bbox_inches='tight')  # 保存为矢量图
plt.show()
