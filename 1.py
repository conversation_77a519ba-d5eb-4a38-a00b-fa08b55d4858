import matplotlib.pyplot as plt
import matplotlib.patches as patches

# 设置全局字体大小
plt.rcParams.update({'font.size': 16})

# 输入点数
points = [256, 512, 1024, 2048, 4096, 8192, 16384, 32768]

# 数据
data = {
    'PointMamba': {
        'FLOPs(G)': [3.845024768] * 8,
        '显存(MB)': [0.29,0.31,0.35,0.5,0.6,1.1,2.3,3.2]
    },
    'PointDefMamba': {
        'FLOPs(G)': [4.782281216] * 8,
        '显存(MB)': [0.5,0.7,1.1,1.5,1.7,2.5,3.1,4.5]
    },
    'Point-MAE': {
        'FLOPs(G)': [4.8, 5, 7, 10, 50, 150, 400, 696],
        '显存(MB)': [4.8, 5, 5.2, 6, 6.5, 8, 15, 50.52]
    }
}



# 画 FLOPs 图
plt.figure(figsize=(12, 6))
for model, values in data.items():
    if model == 'PointDefMamba':
        plt.plot(points, values['FLOPs(G)'], marker='*', color='red', markersize=12, linewidth=2, label=model)
    else:
        plt.plot(points, values['FLOPs(G)'], marker='o', markersize=8, linewidth=2, label=model)

# 在32768位置添加下降箭头和"down"标注
arrow_x = 32768
arrow_y = max([data[model]['FLOPs(G)'][-1] for model in data.keys()]) * 0.8
plt.annotate('down', xy=(arrow_x, arrow_y), xytext=(arrow_x, arrow_y + 100),
            arrowprops=dict(arrowstyle='->', color='black', lw=2),
            fontsize=18, ha='center', va='bottom', weight='bold')

plt.title('FLOPs', fontsize=20, weight='bold')
plt.xlabel('Input Points', fontsize=18, weight='bold')
plt.ylabel('FLOPs (G)', fontsize=18, weight='bold')
plt.xticks(points, [str(p) for p in points], fontsize=14)  # 使用数字而非2的n次方
plt.yticks(fontsize=14)
plt.grid(False)
plt.legend(fontsize=14)
plt.tight_layout()
plt.savefig('flops_chart.svg', format='svg', dpi=300, bbox_inches='tight')  # 保存为矢量图
plt.show()

# 画 显存 图
plt.figure(figsize=(12, 6))
for model, values in data.items():
    if model == 'PointDefMamba':
        plt.plot(points, values['显存(MB)'], marker='*', color='red', markersize=12, linewidth=2, label=model)
    else:
        plt.plot(points, values['显存(MB)'], marker='o', markersize=8, linewidth=2, label=model)

# 在32768位置添加下降箭头和"down"标注
arrow_x = 32768
arrow_y = max([data[model]['显存(MB)'][-1] for model in data.keys()]) * 0.8
plt.annotate('down', xy=(arrow_x, arrow_y), xytext=(arrow_x, arrow_y + 10),
            arrowprops=dict(arrowstyle='->', color='black', lw=2),
            fontsize=18, ha='center', va='bottom', weight='bold')

plt.title('GPU Memory', fontsize=20, weight='bold')
plt.xlabel('Input Points', fontsize=18, weight='bold')
plt.ylabel('Memory Usage (MB)', fontsize=18, weight='bold')
plt.xticks(points, [str(p) for p in points], fontsize=14)  # 使用数字而非2的n次方
plt.yticks(fontsize=14)
plt.grid(False)
plt.legend(fontsize=14)
plt.tight_layout()
plt.savefig('memory_chart.svg', format='svg', dpi=300, bbox_inches='tight')  # 保存为矢量图
plt.show()
